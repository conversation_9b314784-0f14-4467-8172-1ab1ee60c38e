import React from 'react'

const badgeVariants = {
  default: 'bg-gray-900 text-white hover:bg-gray-800',
  secondary: 'bg-gray-100 text-gray-900 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-100 dark:hover:bg-gray-700',
  destructive: 'bg-red-500 text-white hover:bg-red-600',
  outline: 'border border-gray-200 text-gray-900 hover:bg-gray-100 dark:border-gray-700 dark:text-gray-100 dark:hover:bg-gray-800',
  success: 'bg-green-500 text-white hover:bg-green-600',
  warning: 'bg-yellow-500 text-white hover:bg-yellow-600',
  info: 'bg-blue-500 text-white hover:bg-blue-600'
}

const badgeSizes = {
  sm: 'px-2 py-0.5 text-xs',
  default: 'px-2.5 py-0.5 text-sm',
  lg: 'px-3 py-1 text-base'
}

export const Badge = ({ 
  children, 
  variant = 'default', 
  size = 'default',
  className = '', 
  ...props 
}) => {
  const variantClasses = badgeVariants[variant] || badgeVariants.default
  const sizeClasses = badgeSizes[size] || badgeSizes.default

  return (
    <span 
      className={`inline-flex items-center font-medium rounded-full transition-colors ${variantClasses} ${sizeClasses} ${className}`}
      {...props}
    >
      {children}
    </span>
  )
}
