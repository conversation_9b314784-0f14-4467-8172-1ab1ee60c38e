@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@400;500;600&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Noto Sans', Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji';
    scroll-behavior: smooth;
  }

  body {
    @apply bg-gray-50 dark:bg-gray-950 text-gray-900 dark:text-gray-100;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    line-height: 1.5;
  }

  * {
    @apply border-gray-200 dark:border-gray-800;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold tracking-tight text-gray-900 dark:text-gray-100;
  }

  h1 {
    @apply text-3xl md:text-4xl lg:text-5xl;
  }

  h2 {
    @apply text-2xl md:text-3xl lg:text-4xl;
  }

  h3 {
    @apply text-xl md:text-2xl;
  }

  h4 {
    @apply text-lg md:text-xl;
  }

  h5 {
    @apply text-base md:text-lg;
  }

  h6 {
    @apply text-sm md:text-base;
  }
}

@layer components {
  /* Professional Button Styles - GitHub Inspired */
  .btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md
           border border-transparent transition-colors duration-150 ease-in-out
           focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
           disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-secondary {
    @apply bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700
           text-gray-900 dark:text-gray-100 font-medium py-2 px-4 rounded-md
           border border-gray-300 dark:border-gray-600 transition-colors duration-150 ease-in-out
           focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
  }

  .btn-outline {
    @apply bg-transparent hover:bg-gray-50 dark:hover:bg-gray-800
           text-gray-700 dark:text-gray-300 font-medium py-2 px-4 rounded-md
           border border-gray-300 dark:border-gray-600 transition-colors duration-150 ease-in-out
           focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
  }

  .btn-ghost {
    @apply bg-transparent hover:bg-gray-100 dark:hover:bg-gray-800
           text-gray-700 dark:text-gray-300 font-medium py-2 px-3 rounded-md
           transition-colors duration-150 ease-in-out
           focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
  }

  .btn-danger {
    @apply bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md
           border border-transparent transition-colors duration-150 ease-in-out
           focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2
           disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-success {
    @apply bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md
           border border-transparent transition-colors duration-150 ease-in-out
           focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2
           disabled:opacity-50 disabled:cursor-not-allowed;
  }

  /* Professional Card Styles */
  .card {
    @apply bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-800
           p-6 transition-colors duration-150 ease-in-out;
  }

  .card-interactive {
    @apply card hover:border-gray-300 dark:hover:border-gray-700 cursor-pointer;
  }

  .card-feature {
    @apply card hover:border-blue-200 dark:hover:border-blue-800;
  }

  .card-header {
    @apply border-b border-gray-200 dark:border-gray-800 pb-4 mb-4;
  }

  /* Professional Input Styles */
  .input {
    @apply w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md
           bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100
           placeholder-gray-500 dark:placeholder-gray-400
           focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
           transition-colors duration-150 ease-in-out;
  }

  .input-error {
    @apply input border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500;
  }

  .label {
    @apply block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1;
  }

  .form-group {
    @apply mb-4;
  }

  /* Professional Glass Effect */
  .glass {
    @apply bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm border border-gray-200/50 dark:border-gray-800/50;
  }

  /* Subtle Gradient Text */
  .gradient-text {
    @apply bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent;
  }

  /* Section Spacing */
  .section {
    @apply py-16 lg:py-24;
  }

  .section-sm {
    @apply py-12 lg:py-16;
  }

  /* Container */
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* Professional Navigation */
  .nav-link {
    @apply text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100
           px-3 py-2 rounded-md text-sm font-medium transition-colors duration-150 ease-in-out;
  }

  .nav-link-active {
    @apply nav-link text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20;
  }

  /* Professional Status Indicators */
  .status-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .status-success {
    @apply status-badge bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400;
  }

  .status-warning {
    @apply status-badge bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400;
  }

  .status-error {
    @apply status-badge bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400;
  }

  .status-info {
    @apply status-badge bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400;
  }

  /* Text Selection */
  ::selection {
    @apply bg-blue-200 text-blue-900;
  }

  ::-moz-selection {
    @apply bg-blue-200 text-blue-900;
  }

  /* Focus Styles */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900;
  }

  /* Loading States */
  .skeleton {
    @apply bg-gray-200 dark:bg-gray-700 animate-pulse rounded;
  }

  /* Professional Table Styles */
  .table {
    @apply w-full border-collapse;
  }

  .table th {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider
           bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700;
  }

  .table td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100
           border-b border-gray-200 dark:border-gray-700;
  }

  /* Professional Animations */
  @keyframes slideInFromLeft {
    0% { transform: translateX(-100%); opacity: 0; }
    100% { transform: translateX(0); opacity: 1; }
  }

  @keyframes slideInFromRight {
    0% { transform: translateX(100%); opacity: 0; }
    100% { transform: translateX(0); opacity: 1; }
  }

  @keyframes slideInFromTop {
    0% { transform: translateY(-100%); opacity: 0; }
    100% { transform: translateY(0); opacity: 1; }
  }

  @keyframes slideInFromBottom {
    0% { transform: translateY(100%); opacity: 0; }
    100% { transform: translateY(0); opacity: 1; }
  }

  @keyframes zoomIn {
    0% { transform: scale(0.8); opacity: 0; }
    100% { transform: scale(1); opacity: 1; }
  }

  @keyframes shimmer {
    0% { background-position: -200px 0; }
    100% { background-position: calc(200px + 100%) 0; }
  }

  .animate-slide-in-left {
    animation: slideInFromLeft 0.6s ease-out;
  }

  .animate-slide-in-right {
    animation: slideInFromRight 0.6s ease-out;
  }

  .animate-slide-in-top {
    animation: slideInFromTop 0.6s ease-out;
  }

  .animate-slide-in-bottom {
    animation: slideInFromBottom 0.6s ease-out;
  }

  .animate-zoom-in {
    animation: zoomIn 0.4s ease-out;
  }

  .animate-shimmer {
    animation: shimmer 2s infinite linear;
    background: linear-gradient(
      to right,
      #f6f7f8 0%,
      #edeef1 20%,
      #f6f7f8 40%,
      #f6f7f8 100%
    );
    background-size: 800px 104px;
  }

  /* Dark mode shimmer */
  .dark .animate-shimmer {
    background: linear-gradient(
      to right,
      #374151 0%,
      #4b5563 20%,
      #374151 40%,
      #374151 100%
    );
  }

  /* Improved scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-neutral-100 dark:bg-neutral-800;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-neutral-300 dark:bg-neutral-600 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-neutral-400 dark:bg-neutral-500;
  }

  /* Professional focus styles */
  .focus-visible {
    @apply outline-none ring-2 ring-primary-500 ring-offset-2 dark:ring-offset-neutral-900;
  }

  /* Better button states */
  .btn-state {
    @apply relative overflow-hidden;
  }

  .btn-state::before {
    content: '';
    @apply absolute inset-0 bg-white/20 transform scale-x-0 origin-left transition-transform duration-300;
  }

  .btn-state:hover::before {
    @apply scale-x-100;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-400 dark:bg-gray-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-500 dark:bg-gray-500;
}

/* Loading animation */
.loading-dots {
  display: inline-block;
  position: relative;
  width: 80px;
  height: 80px;
}

.loading-dots div {
  position: absolute;
  top: 33px;
  width: 13px;
  height: 13px;
  border-radius: 50%;
  background: #3b82f6;
  animation-timing-function: cubic-bezier(0, 1, 1, 0);
}

.loading-dots div:nth-child(1) {
  left: 8px;
  animation: loading-dots1 0.6s infinite;
}

.loading-dots div:nth-child(2) {
  left: 8px;
  animation: loading-dots2 0.6s infinite;
}

.loading-dots div:nth-child(3) {
  left: 32px;
  animation: loading-dots2 0.6s infinite;
}

.loading-dots div:nth-child(4) {
  left: 56px;
  animation: loading-dots3 0.6s infinite;
}

@keyframes loading-dots1 {
  0% {
    transform: scale(0);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes loading-dots3 {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(0);
  }
}

@keyframes loading-dots2 {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(24px, 0);
  }
}
